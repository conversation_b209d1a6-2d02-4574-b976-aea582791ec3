package com.bukuwarung.activities.bulktransaction.view

import android.app.Dialog
import android.os.Bundle
import android.os.SystemClock
import android.view.Window
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.viewModels
import androidx.fragment.app.FragmentActivity
import com.bukuwarung.R
import com.bukuwarung.activities.bulktransaction.data.BulkTransactionData
import com.bukuwarung.activities.bulktransaction.helpers.IOnBackPressed
import com.bukuwarung.activities.bulktransaction.viewmodel.BulkTransactionViewModel
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.bulk.CashTransactionType
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.subscribeSingleLiveEvent
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BulkTransactionActivity : FragmentActivity() {

    lateinit var fragment: BulkTransactionFragment
    lateinit var backButton: ImageView
    private val bulkTransactionViewModel: BulkTransactionViewModel by viewModels()
    private var lastButtonSaveClicked: Long = 0
    var bulkData: MutableList<BulkTransactionData> = mutableListOf()

    companion object {
        var isValueChanged = false
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_add_bulk_transaction)
        val bulkTransactionData = intent.getSerializableExtra("bulkTransactionData")

        subscribeState()

        fragment = BulkTransactionFragment.instance(bulkTransactionData as BulkTransactionData)

        supportFragmentManager.beginTransaction().add(R.id.container,
                fragment).commit()

        backButton = findViewById(R.id.back)
        backButton.setOnClickListener {
            onBackPressed()
        }

    }

    override fun onBackPressed() {
        if (isValueChanged) {
            (fragment as? IOnBackPressed)?.onBackPressed()?.let {
                if (it.isNotEmpty()) {
                    showExitDialog(it)
                } else {
                    super.onBackPressed()
                }
            }
        } else {
            super.onBackPressed()
        }
    }

    fun showExitDialog(bulkTransactionData: MutableList<BulkTransactionData>) {
        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.dialog_exit_bulk_transaksi)
        val title = dialog.findViewById(R.id.tv_title) as TextView
        val body = dialog.findViewById(R.id.tv_body) as TextView
        val yesBtn = dialog.findViewById(R.id.btn_yes) as Button
        val noBtn = dialog.findViewById(R.id.btn_no) as Button
        title.text = getString(R.string.bulk_dialog_title)
        body.text = getString(R.string.bulk_dialog_body)
        yesBtn.text = getString(R.string.bulk_dialog_yes)
        noBtn.text = "Lanjut Catat"
        yesBtn.setOnClickListener {
            saveTransactions(bulkTransactionData)
            dialog.dismiss()
        }
        noBtn.setOnClickListener { dialog.dismiss() }
        dialog.show()
    }

    fun saveTransactions(bulkTransactionData: MutableList<BulkTransactionData>) {
        if (SystemClock.elapsedRealtime() - lastButtonSaveClicked < 600)
            return
        lastButtonSaveClicked = SystemClock.elapsedRealtime()

        bulkData = bulkTransactionData

        for (i in bulkTransactionData.indices) {
            if (bulkTransactionData[i].transactionId == null) {
                bulkTransactionViewModel.onEventReceived(BulkTransactionViewModel.Event.AddNewTransaction(bulkTransactionData, i, true))
            }
        }
        MainActivity.startActivitySingleTopToTab(this, TabName.TRANSACTION_HOME)
        finish()
    }

    fun subscribeState() {
        subscribeSingleLiveEvent(bulkTransactionViewModel.state) {
            when (it) {
                is BulkTransactionViewModel.State.SetTransactionId -> {
                    bulkData[it.position].transactionId = it.transactionId
                    sendAggregateValuesToAnalytics(bulkData)
                    for (element in bulkData) {
                        sendIndividualValuesToAnalytics(element)
                    }

                }

                else -> {}
            }
        }
    }

    private fun sendAggregateValuesToAnalytics(bulkTransactionData: List<BulkTransactionData>) {
        var salesCount = 0
        var expenseCount= 0
        var totalSales = 0.0
        var total_expense = 0.0

        for (i in bulkTransactionData.indices) {
            salesCount += if (bulkTransactionData[i].credit > 0)  1 else 0
            expenseCount += if (bulkTransactionData[i].sales > 0)  1 else 0
            totalSales = totalSales.plus(if (bulkTransactionData[i].credit > 0) {
                (bulkTransactionData[i].credit - bulkTransactionData[i].harga)
            } else 0.0)
            total_expense = total_expense.plus(if (bulkTransactionData[i].sales > 0) bulkTransactionData[i].sales else 0.0)
        }
    }

    private fun sendIndividualValuesToAnalytics(data: BulkTransactionData) {
        if (data.sales <= 0 && data.credit <= 0 && data.harga <= 0) {
            return
        }
        val salesType = if (data.sales > 0) CashTransactionType.DEBIT else CashTransactionType.CREDIT
    }

}