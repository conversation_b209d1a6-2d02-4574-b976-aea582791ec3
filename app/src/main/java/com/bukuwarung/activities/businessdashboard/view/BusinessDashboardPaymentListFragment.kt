package com.bukuwarung.activities.businessdashboard.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.adapter.BusinessPaymentAdapter
import com.bukuwarung.activities.businessdashboard.adapter.BusinessTransaksiAdapter
import com.bukuwarung.activities.businessdashboard.model.CategoriesItem
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.homepage.view.HomeHelpSectionFragment
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.databinding.FragmentPaymentListBusinessDashboardBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class BusinessDashboardPaymentListFragment: BaseFragment() {

    private lateinit var binding: FragmentPaymentListBusinessDashboardBinding

    private val viewModel: BusinessDashboardMainViewModel by activityViewModels()
    private lateinit var paymentListAdapter: BusinessPaymentAdapter
    private var tagString: String? = null
    private var isFromBottomsheet = false

    companion object {
        const val TAG = "TAG"
        const val IS_FROM_BOTTOMSHEET = "IS_FROM_BOTTOMSHEET"
        fun instance(tag: String, isFromBottomSheet: Boolean): BusinessDashboardPaymentListFragment  {
            val fragment = BusinessDashboardPaymentListFragment()
            fragment.arguments = Bundle().apply {
                putString(TAG, tag)
                putBoolean(IS_FROM_BOTTOMSHEET, isFromBottomSheet)
            }
            return fragment
        }
    }

    override fun setupView(view: View) {

    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentPaymentListBusinessDashboardBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tagString = arguments?.getString(TAG)
        isFromBottomsheet = arguments?.getBoolean(IS_FROM_BOTTOMSHEET) == true
        if (tagString.equals("Tagih")) {
            paymentListAdapter =
                BusinessPaymentAdapter(
                    viewModel.paymentRequestList.value as List<CategoriesItem>,
                    isFromBottomsheet,
                    tagString.orEmpty()
                )
        } else {
            paymentListAdapter =
                BusinessPaymentAdapter(
                    viewModel.disbursementRequestList.value as List<CategoriesItem>,
                    isFromBottomsheet,
                    tagString.orEmpty()
                )
        }
        paymentListAdapter.setHasStableIds(true)
        binding.paymentListRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = paymentListAdapter
        }
    }

    override fun subscribeState() {
      // No imp
    }
}