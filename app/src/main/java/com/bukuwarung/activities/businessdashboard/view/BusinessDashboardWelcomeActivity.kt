package com.bukuwarung.activities.businessdashboard.view

import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityProductDashboardWelcomeBinding
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BusinessDashboardWelcomeActivity: BaseActivity() {
    private lateinit var binding: ActivityProductDashboardWelcomeBinding
    private val viewModel: BusinessDashboardMainViewModel by viewModels()

    companion object {
        const val PPOB_COUNT = "PPOB_COUNT"
        const val PAYMENT_COUNT = "PAYMENT_COUNT"
        var isFromInfo: Boolean = false

        fun createIntent(origin: Context?, ppobCount: Double?, paymentCount: Double?): Intent {
            val intent = Intent(origin, BusinessDashboardWelcomeActivity::class.java)
            intent.putExtra(PPOB_COUNT, ppobCount)
            intent.putExtra(PAYMENT_COUNT, paymentCount)
            return intent
        }
    }

    override fun setViewBinding() {
        binding = ActivityProductDashboardWelcomeBinding.inflate(layoutInflater)
        viewModel.ppobCount = intent.getDoubleExtra(PPOB_COUNT, 0.0)
        viewModel.paymentCount = intent.getDoubleExtra(PAYMENT_COUNT, 0.0)
    }

    override fun setupView() {
        if (!isFromInfo) {
            viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.CheckFirstTimeEntry)
        }
        setContentView(binding.root)
        binding.btnProceed.setSingleClickListener {
            viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.OnProceedClicked)
        }

        binding.imgBack.setSingleClickListener {
            viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.OnBackPressed)
        }

        if (isFromInfo) {
            binding.btnProceed.hideView()
            isFromInfo = false
        } else {
            binding.btnProceed.showView()
        }

    }


    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {it ->
            when (it) {
                is BusinessDashboardMainViewModel.State.IsProductDashboardWelcomeSeen -> {
                    handleRedirection(it.isShown)
                }
                BusinessDashboardMainViewModel.State.ProceedToDashBoard -> {
                    handleRedirection(true)
                }
                BusinessDashboardMainViewModel.State.OnBackPressed -> {
                    onBackPressed()
                }
                else -> {}
            }
        }
    }

    private fun handleRedirection(isShown: Boolean) {
        if (isShown) {
            startActivity(BusinessDashboardMainActivity.createIntent(this, viewModel.ppobCount, viewModel.paymentCount))
            finish()
        }
    }
}