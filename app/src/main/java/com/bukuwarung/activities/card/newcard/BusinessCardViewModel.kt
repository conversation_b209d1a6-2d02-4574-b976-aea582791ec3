package com.bukuwarung.activities.card.newcard

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.work.*
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.recordException
import com.bukuwarung.wrapper.EventWrapper
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.launch
import javax.inject.Inject
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel
class BusinessCardViewModel @Inject constructor(
        private val businessRepository: BusinessRepository,
        private val sessionManager: SessionManager
) : BaseViewModel() {
    private var _bookEntity = MutableLiveData<BookEntity>()
    val bookEntity: LiveData<BookEntity> = _bookEntity

    private val _businessCardDesignLiveData = MutableLiveData<List<BusinessCardDesign>>()
    val businessCardDesignLiveData: LiveData<List<BusinessCardDesign>> = _businessCardDesignLiveData

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state

    init {
        _bookEntity.value = businessRepository.getBusinessByIdSync(sessionManager.businessId)
        _businessCardDesignLiveData.value = getBusinessCardDesigns()
    }

    fun refreshBookData() {
        _bookEntity.value = businessRepository.getBusinessByIdSync(sessionManager.businessId)
    }

    private fun getBusinessCardDesigns(): List<BusinessCardDesign> {
        try {
            val json = RemoteConfigUtils.NewBusinessCard.getDesigns()
            val type = object : TypeToken<List<BusinessCardDesign>>() {}.type
            var businessCard: List<BusinessCardDesign> = Gson().fromJson(json, type)
            return getUnlockedCard() + businessCard;
        } catch (ex: Exception) {
            ex.recordException()
            return emptyList()
        }
    }

    private fun getUnlockedCard(): List<BusinessCardDesign> {
        //append unlocked cards if additional requirement is fullfilled
        if(Utility.isSectionFilled(_bookEntity.value,Utility.profileAdditionalInfoFields)){
            return RemoteConfigUtils.NewBusinessCard.getLockedDesigns()
        }
        return emptyList()
    }

    fun updateBusinessData(book: BookEntity?) {
        _bookEntity.value = book
    }

    fun saveBusinessCard(address: Address?) = viewModelScope.launch {
        val book = bookEntity.value ?: return@launch
        businessRepository.updateBusinessProfileComplete(
                sessionManager.userId,
                sessionManager.deviceId,
                sessionManager.businessId,
                book.businessName,
                book.businessOwnerName,
                book.businessTagLine,
                book.businessPhone,
                book.businessAddress,
                book.businessEmail
        )

        address?.let { businessRepository.updateBusinessAdministrativeData(book.bookId, it) }

        _bookEntity.value = businessRepository.getBusinessByIdSync(sessionManager.businessId)

        setState(State.OnSaved)
    }

    sealed class State {
        object OnSaved : State()
    }

    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }

}