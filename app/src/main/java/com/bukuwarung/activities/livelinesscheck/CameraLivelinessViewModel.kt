package com.bukuwarung.activities.livelinesscheck

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.os.Build
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.BuildConfig
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.AppConst.LIVELINESS_URL
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.liveliness.UploadPhotoUseCase
import com.bukuwarung.los.model.LivelinessResponse
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.MultipartBody.Part.Companion.createFormData
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.*
import javax.inject.Inject


@HiltViewModel
class CameraLivelinessViewModel @Inject constructor(private val uploadPhotoUseCase: UploadPhotoUseCase) :
    BaseViewModel() {

    private val livelinessLiveData = MutableLiveData<DetailEvent>()
    val observeLivelinessDetail: LiveData<DetailEvent> = livelinessLiveData

    sealed class DetailEvent {
        data class ApiError(val isServerError: Boolean) :
            CameraLivelinessViewModel.DetailEvent()

        data class ApiSuccess(val response: LivelinessResponse) :
            CameraLivelinessViewModel.DetailEvent()

    }

    fun uploadImage(productId: String, file: File?, compressFile: File) = viewModelScope.launch {

        val file = compressImage(file, compressFile) ?: return@launch
        val requestBody: RequestBody = file.asRequestBody("image/octet-stream".toMediaTypeOrNull())
        val filePart: MultipartBody.Part = createFormData("file", file.name, requestBody)
        val productIdRequest: RequestBody =
            productId.toRequestBody("text/plain".toMediaTypeOrNull())

        when (val result =
            uploadPhotoUseCase.uploadPhoto(LIVELINESS_URL, productIdRequest, filePart)) {
            is ApiSuccessResponse -> {
                if (result?.body != null) {
                    livelinessLiveData.value = DetailEvent.ApiSuccess(result.body)
                } else {
                    livelinessLiveData.value = DetailEvent.ApiError(true)
                }
            }
            is ApiErrorResponse -> {
                livelinessLiveData.value = DetailEvent.ApiError(true)
            }
            else -> {}
        }
    }

    private fun compressImage(fileToChange: File?, fileConverted: File): File? {
        val options = BitmapFactory.Options()
        options.inPreferredConfig = Bitmap.Config.ARGB_8888
        var bitmap: Bitmap? = null
        try {
            bitmap = BitmapFactory.decodeStream(FileInputStream(fileToChange), null, options)
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
        if (bitmap != null) {


            if (fileToChange != null) {
                val ei: ExifInterface = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    ExifInterface(fileToChange)
                } else {
                    ExifInterface(fileToChange.absolutePath)
                }

                val orientation: Int =
                    ei.getAttributeInt(
                        ExifInterface.TAG_ORIENTATION,
                        ExifInterface.ORIENTATION_NORMAL
                    )
                var angle = 0f
                when (orientation) {
                    ExifInterface.ORIENTATION_ROTATE_90 -> angle = 90f
                    ExifInterface.ORIENTATION_ROTATE_180 -> angle = 180f
                    ExifInterface.ORIENTATION_ROTATE_270 -> angle = 270f
                    else -> bitmap
                }
                val matrix = Matrix()
                matrix.postRotate(angle)
                bitmap = Bitmap.createBitmap(
                    bitmap, 0, 0, bitmap?.getWidth() ?: 0, bitmap?.getHeight() ?: 0,
                    matrix, true
                )

            }

            if (fileConverted.exists()) {
                fileConverted.delete()
            }
            val MAX_IMAGE_SIZE = 1000 * 800
            var streamLength = MAX_IMAGE_SIZE
            var compressQuality = 105
            val bmpStream = ByteArrayOutputStream()
            while (streamLength >= MAX_IMAGE_SIZE && compressQuality > 5) {
                try {
                    bmpStream.flush() //to avoid out of memory error
                    bmpStream.reset()
                } catch (e: IOException) {
                    FirebaseCrashlytics.getInstance().recordException(e)
                }
                compressQuality -= 5
                bitmap?.compress(Bitmap.CompressFormat.JPEG, compressQuality, bmpStream)
                val bmpPicByteArray = bmpStream.toByteArray()
                streamLength = bmpPicByteArray.size
            }

            val fo: FileOutputStream
            try {
                fo = FileOutputStream(fileConverted)
                fo.write(bmpStream.toByteArray())
                fo.flush()
                fo.close()
            } catch (e: IOException) {
                FirebaseCrashlytics.getInstance().recordException(e)
            }
            return fileConverted

        } else {
            return null
        }

    }

}