package com.bukuwarung.activities.onboarding.form

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.bukuwarung.databinding.CategorySelectorFragmentBinding
import com.bukuwarung.dialogs.base.BaseDialogFragment
import com.bukuwarung.utils.asVisibility
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class CategorySelectorFragment : BaseDialogFragment() {
    private lateinit var binding: CategorySelectorFragmentBinding
    private val data = mutableListOf<CategoryOption>()
    private val vm: DetailedBusinessFormViewModel by activityViewModels()
    private var selectedCategory: CategoryOption? = null
    private var iCategoryCommunicator: ICategoryCommunicator? = null

    private var formType: FormType = FormType.BusinessCategory

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = CategorySelectorFragmentBinding.inflate(inflater)
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        iCategoryCommunicator = context as? ICategoryCommunicator
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(requireArguments()) {
            binding.apply {
                tvToolBarTitle.text = getString(HEADER_TEXT)
                btnAction.text = getString(ACTION_BUTTON_TEXT)
                val useToolbar = getBoolean(SHOW_HEADER)
                val useSubmitButton = getBoolean(SHOW_ACTION_BUTTON)
                toolbar.visibility = useToolbar.asVisibility()
                btnAction.visibility = useSubmitButton.asVisibility()
            }
        }

        binding.btnAction.setOnClickListener {
            vm.categoryFormAction(formType,selectedCategory?.name?:"-",selectedCategory?.resourceId.toString())
            // Implement an interface here to communicate for category
            iCategoryCommunicator?.setCategory(selectedCategory?.name?:"-", selectedCategory?.resourceId.toString())
            dismiss()
        }

        binding.backBtn.setOnClickListener {
            dismiss()
        }

        binding.rvCategory.apply {
            adapter = OnBoardingCategoryAdapter {
                selectedCategory = it
                validateForm()
            }.apply {
                setData(data)
            }
            layoutManager = GridLayoutManager(requireContext(), 3)
        }
    }

//    override fun setupView(view: View) {

//        with(requireArguments()) {
//            binding.apply {
//                tvToolBarTitle.text = getString(HEADER_TEXT)
//                btnAction.text = getString(ACTION_BUTTON_TEXT)
//                val useToolbar = getBoolean(SHOW_HEADER)
//                val useSubmitButton = getBoolean(SHOW_ACTION_BUTTON)
//                toolbar.visibility = useToolbar.asVisibility()
//                btnAction.visibility = useSubmitButton.asVisibility()
//            }
//        }
//
//        binding.btnAction.setOnClickListener {
//            requireActivity().supportFragmentManager.beginTransaction().remove(this).commit()
//            vm.categoryFormAction(formType,selectedCategory?.name?:"-",selectedCategory?.resourceId.toString())
//        }
//
//        binding.backBtn.setOnClickListener {
//            requireActivity().supportFragmentManager.beginTransaction().remove(this).commit()
//        }
//
//        binding.rvCategory.apply {
//            adapter = OnBoardingCategoryAdapter {
//                selectedCategory = it
//                validateForm()
//            }.apply {
//                setData(data)
//            }
//            layoutManager = GridLayoutManager(requireContext(), 3)
//        }
//    }

    private fun validateForm() {
        binding.btnAction.isEnabled = selectedCategory != null
    }

    private fun setData(_data: List<CategoryOption>) {
        data.apply {
            clear()
            addAll(_data)
        }
    }

    private fun setFormType(_formType: FormType) {
        formType = _formType
    }

    companion object {
        private const val HEADER_TEXT = "header_text"
        private const val SHOW_HEADER = "show_header"
        private const val SHOW_ACTION_BUTTON = "show_action_button"
        private const val ACTION_BUTTON_TEXT = "action_button_text"

        fun getInstance(
            header: String = "Kategori Usaha",
            actionBtnText: String = "Simpan",
            showHeader: Boolean = true,
            showActionButton: Boolean = true,
            formType: FormType = FormType.BusinessCategory,
            data: List<CategoryOption>
        ): CategorySelectorFragment {
            val bundle = Bundle().apply {
                putString(HEADER_TEXT, header)
                putString(ACTION_BUTTON_TEXT, actionBtnText)
                putBoolean(SHOW_HEADER, showHeader)
                putBoolean(SHOW_ACTION_BUTTON, showActionButton)
            }

            return CategorySelectorFragment().apply {
                arguments = bundle
                setData(data)
                setFormType(formType)
            }
        }
    }

    interface ICategoryCommunicator {
        fun setCategory(category: String, categoryId: String = "")
    }

}