package com.bukuwarung.activities.profile.update

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.profile.update.dialogs.DeleteBusinessDialog
import com.bukuwarung.activities.referral.share.UploadReferralImageActivity
import com.bukuwarung.activities.successmessage.ProfileSuccessMessageActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.ReferralRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.ActivityBusinessProfileFormBinding
import com.bukuwarung.dialogs.businessselector.BusinessSelectorDialog
import com.bukuwarung.dialogs.businessselector.BusinessSelectorDialog.BusinessSelectedListener
import com.bukuwarung.dialogs.businessselector.BusinessType
import com.bukuwarung.dialogs.loading.LoadingDialog
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectViewHolderType
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import javax.inject.Inject
import androidx.activity.viewModels
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BusinessProfileFormActivity : BaseActivity(), BusinessSelectedListener {

    private val viewModel: BusinessProfileFormViewModel by viewModels()
    private lateinit var binding: ActivityBusinessProfileFormBinding
    private var bookEntity: BookEntity? = null
    private var businessRepository: BusinessRepository? = null
    var dialog: BusinessSelectorDialog? = null
    private var isRedirectedFromPayments = false

    override fun setViewBinding() {
        binding = ActivityBusinessProfileFormBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        businessRepository = BusinessRepository.getInstance(Application.getAppContext())
        bookEntity = businessRepository?.getBusinessByIdSync(User.getBusinessId())
        setupToolbar()
        bindLayoutComponents()
        initEditForm()
        if (intent.hasExtra(isRedirectFromPaymentsToProfile)) {
            isRedirectedFromPayments = true
        }
    }

    override fun subscribeState() {
    }

    private fun bindLayoutComponents() {
        binding.save.setOnClickListener {
            onClickSave()
            setResult(RESULT_OK)
            val txnCount = TransactionRepository.getInstance(applicationContext).transactionCount
            Utilities.sendEventsToBackendWithBureau(AnalyticsConst.BUSINESS_PROFILE_SAVE, "business_form")
            if (!isRedirectedFromPayments) {
                if (txnCount == AppConst.ZERO_TRANSACTIONS) {
                    val intent = Intent(
                        Application.getAppContext(),
                        ProfileSuccessMessageActivity::class.java
                    )
                    startActivity(intent)
                }
            }
        }
        val activity = this
        binding.deleteBusinessBtn.setOnClickListener { showDeletionDialog(activity) }
        binding.ownerName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun afterTextChanged(editable: Editable) {
                checkSaveBtnEnabledAndError()
            }
        })
        binding.bizName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun afterTextChanged(editable: Editable) {
                checkSaveBtnEnabledAndError()
            }
        })
    }

    private fun checkSaveBtnEnabledAndError() {
        val name = binding.ownerName.text.toString()
        val bizName = binding.bizName.text.toString()
        val bookType = if (bookEntity == null) -1 else bookEntity!!.bookType
        binding.save.isEnabled = Utility.hasBusinessName(name) && Utility.hasBusinessName(bizName) && bookType != -1
        ComponentUtil.setVisible(binding.ownerNameError, !Utility.hasBusinessName(name))
        ComponentUtil.setVisible(binding.businessError, !Utility.hasBusinessName(bizName))
        ComponentUtil.setVisible(binding.businessError, !Utility.hasBusinessName(bizName))
        ComponentUtil.setVisible(binding.businessTypeError, bookType == -1)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        val supportActionBar = supportActionBar
        supportActionBar!!.setDisplayHomeAsUpEnabled(true)
        val activity = this
        binding.toolbar.setNavigationOnClickListener(View.OnClickListener {
            InputUtils.hideKeyBoardWithCheck(activity)
            setResult(RESULT_CANCELED)
            activity.onBackPressed()
        })
    }

    private fun initEditForm() {
        if (bookEntity != null) {
            binding.ownerName.setText(bookEntity!!.businessOwnerName)
            binding.bizName.setText(bookEntity!!.businessName)

            val businessTypes = AppConfigManager.getInstance().businessTypes

            // this to ensure predefined category will always be there no matter what
            val defaultBusinessType = AppConst.defaultBusinessType(this)
            val activity: Activity = this
            if (bookEntity!!.bookType == null) {
                bookEntity!!.bookType = -1
            }
            var selectedBusiness = BusinessType(bookEntity!!.bookType, if (Utility.isBlank(bookEntity!!.bookTypeName)) "" else bookEntity!!.bookTypeName)
            for (businessType in defaultBusinessType) {
                // if predefined category doesn't exist for some reasons, we add it.
                if (!businessTypes.contains(businessType)) businessTypes.add(businessType)
            }
            try {
                // custom business type saved from previous session, we need to add to sharedpref
                if (selectedBusiness.getName() != "" && !businessTypes.contains(selectedBusiness)) {
                    selectedBusiness.setName(bookEntity!!.bookTypeName)
                    selectedBusiness.setType(SelectableObjectViewHolderType.SECONDARY_CONTENT)
                    businessTypes.add(selectedBusiness)
                }
                if (bookEntity != null && selectedBusiness.getName() != "" && businessTypes.contains(selectedBusiness)) {
                    val index = businessTypes.indexOf(selectedBusiness)
                    selectedBusiness = businessTypes[index]
                    binding.businessTypeET.setText(selectedBusiness.getName())
                } else if (bookEntity != null) {
                    // custom business category saved from prev. session
                    binding.businessTypeET.setText(bookEntity!!.bookTypeName)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            binding.businessTypeET.setOnClickListener { showBusinessTypeDlg(activity, businessTypes, bookEntity!!.bookType) }
            checkSaveBtnEnabledAndError()
        }
    }

    fun showBusinessTypeDlg(activity: Activity?, businessTypes: List<BusinessType>?,
                            bookType: Int) {
        dialog = null // get rid of old instance
        dialog = BusinessSelectorDialog.getInstance(
                activity,
                businessTypes,
                this
        )
        if (bookType != 1) dialog?.selectedId = bookType
        dialog?.show()
    }

    override fun onBusinessSelected(datum: SelectableObject) {
        bookEntity!!.bookType = datum.getId()
        bookEntity!!.bookTypeName = datum.getName()
        binding.businessTypeET.setText(datum.getName().toString())
        dialog!!.dismiss()
        checkSaveBtnEnabledAndError()
    }

    override fun onNewBusinessAdded(datum: SelectableObject) {
        bookEntity!!.bookType = datum.getId()
        bookEntity!!.bookTypeName = datum.getName()
        binding.businessTypeET.setText(datum.getName())
        if (Utility.isEqual(datum.getName(), "Lainnya")) {
            bookEntity!!.bookType = 36
        }
        checkSaveBtnEnabledAndError()
        if (dialog != null) dialog!!.dismiss()
    }

    fun onClickSave() {
        val businessName = binding.bizName.text.toString()
        val businessOwnerName = binding.ownerName.text.toString()
        if (bookEntity == null) {
            bookEntity = BusinessRepository.getInstance(this).getBusinessByIdSync(User.getBusinessId())
        }
        if (bookEntity!!.bookType == null) {
            NotificationUtils.alertToast(this.getString(R.string.select_business))
            return
        }
        businessRepository?.updateBusinessProfile(User.getUserId(), User.getDeviceId(), User.getBusinessId(), businessName, bookEntity!!.bookType, bookEntity!!.bookTypeName, businessOwnerName)
        viewModel.updateBusinessName(businessName)
        try {
            if (Utility.hasBusinessName(businessOwnerName)) {
                //MoEHelper.getInstance(this).setFullName(businessOwnerName)
                val prop = HashMap<String, Any>()
                prop["businessOwnerName"] = businessOwnerName
                prop["bookType"] = bookEntity!!.bookType
                AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_TYPE, bookEntity!!.bookType.toString())
                AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_TYPE_NAME, bookEntity!!.bookTypeName)
                AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_NAME, bookEntity!!.businessName)
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.BUSINESS_TYPE, bookEntity!!.bookType)
                propBuilder.put(AnalyticsConst.BUSINESS_TYPE_NAME, bookEntity!!.bookTypeName)
                propBuilder.put(AnalyticsConst.BUSINESS_NAME, bookEntity!!.businessName)
                AppAnalytics.trackEvent(AnalyticsConst.BUSINESS_PROFILE_SAVE, propBuilder)
                Utilities.sendEventsToBackendWithBureau(AnalyticsConst.BUSINESS_PROFILE_SAVE, "business_form")
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.BUSINESS_PROFILE_SAVE, this)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val referralFeatureActive = AppConfigManager.getInstance().useReferral()
        if (referralFeatureActive && businessOwnerName.isNotEmpty()) {
            val dialog = LoadingDialog(this)
            dialog.show()
            try {
                ReferralRepository.getInstance().updateLeaderboardName(
                        businessOwnerName
                ) {
                    dialog.dismiss()
                    val returnIntent = Intent()
                    returnIntent.putExtra(UploadReferralImageActivity.BUSINESS_NAME_PARAM, businessName)
                    returnIntent.putExtra(UploadReferralImageActivity.OWNER_NAME_PARAM, businessOwnerName)
                    setResult(RESULT_OK, returnIntent)
                    //[IMP]bookentity was updated in Application context, BusinessRepository.getInstance(this).getBusinessByIdSync(User.getBusinessId()); will give non updated value
                    val newBookEntity = businessRepository?.getBusinessByIdSync(User.getBusinessId())
                    // try adding points for profile completion if feasible
                    ReferralRepository.getInstance().addTransactionPoints(newBookEntity, true)
                    finish()
                }
            } catch (ex: Exception) {
                Log.e("BusinessProfileForm", "Exception", ex)
                setResult(RESULT_CANCELED)
                dialog.dismiss()
                finish()
            }
        } else {
            if (isRedirectedFromPayments) setResult(RESULT_OK) else setResult(RESULT_CANCELED)
            finish()
        }
        AppAnalytics.trackEvent("save_profile")
    }

    override fun onResume() {
        super.onResume()
        if (bookEntity != null) {
            binding.ownerName.setText(bookEntity!!.businessOwnerName)
            binding.bizName.setText(bookEntity!!.businessName)
        }
    }

    private fun showDeletionDialog(activity: Activity) {

        // book entity can be null some times
        var businessName: String? = "-"
        if (bookEntity != null) businessName = bookEntity!!.businessName
        val dialog = DeleteBusinessDialog(
                activity,
                businessName) { promptResult: Boolean ->
            if (promptResult) {
                deleteBook()
            }
        }
        dialog.show()
    }

    private fun deleteBook() {
        businessRepository?.updateDeleteFlag(User.getUserId(), User.getDeviceId(), User.getBusinessId(), Integer.valueOf(1))
        MainActivity.startActivityAndClearTop(this, "open_side_menu", true)
    }

    companion object {
        private const val isRedirectFromPaymentsToProfile = "isRedirectFromPaymentsToProfile"
        fun getIntent(origin: Activity?): Intent {
            return Intent(origin, BusinessProfileFormActivity::class.java)
        }
    }
}