package com.bukuwarung.activities.supplier

import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.provider.ContactsContract
import android.util.Patterns
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.activity.viewModels
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.app.ActivityCompat
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.loader.content.CursorLoader
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.activities.supplier.model.AddSupplierRequestBody
import com.bukuwarung.activities.supplier.model.EditSupplierRequestBody
import com.bukuwarung.activities.supplier.model.GetSupplierResponse
import com.bukuwarung.activities.supplier.model.VerifyPhoneNumberResponseBody
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.databinding.ActivityAddSupplierBinding
import com.bukuwarung.dialogs.contact.ContactPermissionBottomSheetDialog
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddSupplierActivity : BaseActivity(), ContactPermissionBottomSheetDialog.ContactPermissionSheetListener {

    private val viewModel: AddSupplierViewModel by viewModels()

    private lateinit var binding: ActivityAddSupplierBinding
    private var isEditSupplier = false
    private var oldSupplierId: String? = null
    private var newSupplierId: String? = null
    private var supplierStoreId: String? = null
    private var isTokokoVerified = false
    private var isFromContactList = false

    companion object {
        const val PICK_CONTACT = 4321
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.btnSave.isEnabled = false
        if (intent.hasExtra("supplierId")) {
            with(binding) {
                etStoreName.isEnabled = false
                etShopLink.isEnabled = false
            }
            oldSupplierId = intent.getStringExtra("supplierId").orEmpty()
            isEditSupplier = true
            viewModel.submitEvent(AddSupplierViewModel.Event.GetSupplierDetails(oldSupplierId.toString()))
            binding.pbProgress.visibility = View.VISIBLE
            binding.countryPicker.isEnabled = false
            binding.countryPicker.setCcpClickable(false)
        } else {
            with(binding) {
                etStoreName.isEnabled = false
                etShopLink.isEnabled = false
                etShopLink.setText(getString(R.string.http_bold_underline))
                ibImportContact.setImageDrawable(
                    AppCompatResources.getDrawable(
                        ibImportContact.context,
                        R.drawable.ic_import_contact
                    )
                )
            }
            isEditSupplier = false
            binding.pbProgress.hideView()
        }

    }

    override fun setViewBinding() {
        binding = ActivityAddSupplierBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        addTextWatchers()
        initClickListeners()
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is AddSupplierViewModel.State.OnSupplierAdded -> handleResponseForAddSupplier(it.result)
                is AddSupplierViewModel.State.OnPhoneNumberVerified -> handleResponseForVerifyPhoneNumber(it.responseBody)
                is AddSupplierViewModel.State.ShowSupplierDetails -> handleResponseForShowSupplierDetails(it.response)
                is AddSupplierViewModel.State.OnSupplierEdited -> handleResponseForEditSupplier(it.result)
            }
        }
    }

    private fun handleResponseForEditSupplier(result: Boolean?) {
        when (result) {
            null -> {
                NotificationUtils.alertToastWithColor(
                    "Terjadi kesalahan pada sistem, silakan coba lagi nanti", this,
                    R.color.green5, R.color.green40
                )
                finish()
            }
            else -> {
                if (result) {
                    finish()
                } else {
                    NotificationUtils.alertToastWithColor(
                        "Terjadi kesalahan pada sistem, silakan coba lagi nanti", this,
                        R.color.green5, R.color.green40
                    )
                    finish()
                }
            }
        }
        binding.pbProgress.hideView()
    }

    private fun handleResponseForShowSupplierDetails(response: GetSupplierResponse?) {
        binding.etPhoneNumber.isEnabled = false
        when (response) {
            null -> {

            }
            else -> {
                with(binding) {
                    if (response.tokokoVerified.isFalseOrNull) {
                        with(binding) {
                            etStoreName.isEnabled = true
                            etShopLink.isEnabled = true
                            ibLinkVerified.hideView()
                            ibImportContact.hideView()
                        }
                        isTokokoVerified = false
                    } else {
                        with(binding) {
                            etStoreName.isEnabled = false
                            etShopLink.isEnabled = false
                            ibLinkVerified.showView()
                            ibImportContact.setImageDrawable(AppCompatResources.getDrawable(ibImportContact.context,R.drawable.ic_tick_green))
                        }
                        isTokokoVerified = true
                    }
                    countryPicker.textView_selectedCountry.text = response.countryCode
                    etPhoneNumber.setText(
                        response.phoneNumber
                    )
                    etStoreName.setText(response.storeName)
                    etShopLink.setText(
                        getString(R.string.http_bold_underline).plus(
                            response.storeLinks?.getOrNull(
                                0
                            )?.substring(
                                8, response.storeLinks.getOrNull(
                                    0
                                )?.length.orNil
                            )
                        )
                    )
                    if (response.storeLinks?.size == 2 && response.storeLinks.getOrNull(1)!= null) {
                        etShopLinkSecond.isEnabled = true
                        etShopLinkSecond.showView()
                        tvShopLinkSecond.showView()
                        etShopLinkSecond.setText(
                            getString(R.string.http_bold_underline).plus(
                                response.storeLinks.getOrNull(
                                    1
                                )?.substring(
                                    8, response.storeLinks.getOrNull(
                                        1
                                    )?.length.orNil
                                )
                            )
                        )
                    } else {
                        tvShopLinkSecond.hideView()
                        etShopLinkSecond.hideView()
                    }

                }
            }
        }
        binding.pbProgress.hideView()
    }

    private fun handleResponseForAddSupplier(result: Boolean?) {
        when (result) {
            null -> {
                NotificationUtils.alertToastWithColor(
                    "Terjadi kesalahan pada sistem, silakan coba lagi nanti", this,
                    R.color.green5, R.color.green40
                )
                finish()
            }
            else -> {
                if (result) {
                    NotificationUtils.alertToastWithColor(
                        "Supplier berhasil ditambahkan", this,
                        R.color.green5, R.color.green40
                    )
                    onBackPressed()
                } else {
                    NotificationUtils.alertToastWithColor(
                        "Terjadi kesalahan pada sistem, silakan coba lagi nanti", this,
                        R.color.green5, R.color.green40
                    )
                    finish()
                }
            }
        }
        binding.pbProgress.hideView()
    }

    private fun handleResponseForVerifyPhoneNumber(response: VerifyPhoneNumberResponseBody?) {
        when (response) {
            null -> {
                with(binding) {
                    etStoreName.isEnabled = true
                    etShopLink.isEnabled = true
                    etShopLink.setText(getString(R.string.http_bold_underline))
                    ibLinkVerified.hideView()
                    ibImportContact.setImageDrawable(
                        AppCompatResources.getDrawable(
                            ibImportContact.context,
                            R.drawable.ic_import_contact
                        )
                    )
                    tvWarning.showView()
                    tvSucces.hideView()
                    tvWarning.addRightImage(AppCompatResources.getDrawable(tvWarning.context,R.drawable.ic_blue_cross))
                }
                isTokokoVerified = false
                binding.btnSave.isEnabled = false
            }
            else -> {
                supplierStoreId = response.supplierStoreId
                newSupplierId = response.supplierId
                with(binding) {
                    ibLinkVerified.showView()
                    ibImportContact.setImageDrawable(
                        AppCompatResources.getDrawable(
                            ibImportContact.context,
                            R.drawable.ic_tick_green
                        )
                    )
                    etStoreName.setText(response.storeName)
                    etShopLink.setText(getString(R.string.http_bold_underline).plus("tokoko.id/").plus(response.storeLink))
                    etStoreName.isEnabled = false
                    etShopLink.isEnabled = false
                    tvSucces.showView()
                    tvWarning.hideView()
                    tvSucces.addRightImage(AppCompatResources.getDrawable(tvWarning.context, R.drawable.ic_green_cross))
                }
                isTokokoVerified = true
                binding.btnSave.isEnabled = true
            }
        }
        binding.pbProgress.hideView()
    }

    private fun initClickListeners() {
        with(binding) {
            ivBack.setOnClickListener {
                onBackPressed()
            }

            ibImportContact.setOnClickListener {
                if (oldSupplierId.isNullOrEmpty()) {
                    val dialog = ContactPermissionBottomSheetDialog.newInstance(
                        AnalyticsConst.ADD_SUPPLIER_PAGE
                    )
                    dialog.show(supportFragmentManager, ContactPermissionBottomSheetDialog.TAG)
                }
            }

            btnCheckNumber.setOnClickListener {
                if (it.isEnabled) {
                    pbProgress.visibility = View.VISIBLE
                    viewModel.submitEvent(
                        AddSupplierViewModel.Event.VerifyPhoneNumber(
                            countryPicker.textView_selectedCountry.text.toString().plus(etPhoneNumber.text.toString())
                        )
                    )
                }
            }

            btnSave.setOnClickListener {
                if (it.isEnabled) {
                    pbProgress.visibility = View.VISIBLE
                    if (oldSupplierId.isNotNullOrEmpty()) {
                        val addSupplierRequestBody =
                            EditSupplierRequestBody(
                                etStoreName.text.toString(),
                                if (etShopLinkSecond.isVisible) etShopLinkSecond.text.toString() else null,
                                isTokokoVerified,
                                etShopLink.text.toString()
                            )
                        viewModel.submitEvent(
                            AddSupplierViewModel.Event.EditSupplier(
                                addSupplierRequestBody, oldSupplierId
                            )
                        )
                    } else {
                        val addSupplierRequestBody =
                            AddSupplierRequestBody(
                                etStoreName.text.toString(),
                                etShopLink.text.toString(),
                                etPhoneNumber.text.toString(),
                                isTokokoVerified,
                                supplierStoreId.orEmpty(),
                                newSupplierId.orEmpty(),
                                countryPicker.textView_selectedCountry.text.toString()
                            )
                        viewModel.submitEvent(
                            AddSupplierViewModel.Event.AddSupplier(
                                addSupplierRequestBody
                            )
                        )
                    }
                }
            }
        }
    }

    private fun addTextWatchers() {
        with(binding) {
            etPhoneNumber.addTextChangedListener {
                if (pbProgress.visibility == View.GONE) {
                    etStoreName.isEnabled = false
                    etShopLink.isEnabled = false
                    btnCheckNumber.isEnabled = it?.length.orNil >= 8 && oldSupplierId==null
                    ibLinkVerified.hideView()
                    etShopLink.setText(getString(R.string.http_bold_underline))
                    ibImportContact.setImageDrawable(
                        AppCompatResources.getDrawable(
                            ibImportContact.context,
                            R.drawable.ic_import_contact
                        )
                    )
                }
                toggleSaveBtn()
            }

            etShopLink.addTextChangedListener {
                toggleSaveBtn()
            }
            etStoreName.addTextChangedListener {
                toggleSaveBtn()
            }
            etShopLinkSecond.addTextChangedListener {
                toggleSaveBtn()
            }
            countryPicker.textView_selectedCountry.addTextChangedListener {
                if (pbProgress.visibility == View.GONE) {
                    etStoreName.isEnabled = false
                    etShopLink.isEnabled = false
                    btnCheckNumber.isEnabled = etPhoneNumber.text.toString().length >= 8 && oldSupplierId==null
                    ibLinkVerified.hideView()
                    ibImportContact.setImageDrawable(
                        AppCompatResources.getDrawable(
                            ibImportContact.context,
                            R.drawable.ic_import_contact
                        )
                    )
                }
                toggleSaveBtn()
            }
        }
    }

    private fun toggleSaveBtn() {
        with(binding) {
            btnSave.isEnabled =
                etShopLink.text?.length.orNil > 8 && pbProgress.visibility == View.GONE && etStoreName.text.toString()
                    .isNotEmpty() && Patterns.WEB_URL.matcher(etShopLink.text.toString()).matches()
        }
    }

    private fun openContactIntent() {
        val i = Intent(Intent.ACTION_PICK, ContactsContract.Contacts.CONTENT_URI)
        i.type = ContactsContract.CommonDataKinds.Phone.CONTENT_TYPE
        startActivityForResult(i, PICK_CONTACT)
    }

    override fun allowPermission() {
        if (!PermissonUtil.hasContactPermission()) {
            ActivityCompat.requestPermissions(
                this, PermissionConst.READ_WRITE_CONTACTS, PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION
            )
        } else {
            openContactIntent()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<out String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            openContactIntent()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PICK_CONTACT && resultCode == RESULT_OK && data != null) {
            data.data?.let { contactData ->
                CursorLoader(this, contactData, null, null, null, null).let { loader ->
                    loader.loadInBackground()?.let { cursor ->
                        if (cursor.moveToFirst()) {
                            binding.etPhoneNumber.setText(cursor.getString(cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)))
                        }
                    }
                }
            }
            isFromContactList = true
        }
    }

}