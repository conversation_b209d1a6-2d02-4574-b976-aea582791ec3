package com.bukuwarung.activities.transaction.category;

import static com.bukuwarung.constants.AnalyticsConst.BD;
import static com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT;
import static com.bukuwarung.constants.AnalyticsConst.NO_TRANSACTION;
import static com.bukuwarung.constants.AnalyticsConst.SELECT_ALL_CHECKED;
import static com.bukuwarung.constants.AnalyticsConst.TRANSAKSI;
import static com.bukuwarung.constants.AnalyticsConst.TYPE;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.categorydetail.CashDetailActivity;
import com.bukuwarung.activities.expense.data.Category;
import com.bukuwarung.activities.expense.detail.DeleteOrUpdateSelectedTransactionRunnable;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.ChangeTransactionCategoryBottomSheet;
import com.bukuwarung.activities.transaction.category.adapter.CategoryTransactionAdapter;
import com.bukuwarung.activities.transaction.category.adapter.dataholder.CategoryTransactionDataHolder;
import com.bukuwarung.activities.transaction.category.observer.CategoryLiveDataObserver;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.dto.CategorySummaryModel;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.material.button.MaterialButton;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public final class CategoryTransactionsActivity extends AppActivity implements View.OnClickListener {

    public static int CREDIT = 1;
    public static int DEBIT = -1;

    public CashCategoryEntity categoryEntity;
    private String categoryId;
    private String brickInstitutionId;
    private String startDate="";
    private String endDate="";
    Toolbar toolbar;
    public TransactionListViewModel transactionListViewModel;

    MaterialButton btnGiveMoney;
    MaterialButton btnReceivePayment;

    public TextView effectiveBalanceTv;
    public TextView balanceTypeStr;
    public TextView tvColMoneyIn;
    public TextView tvRowStatus;
    private TextView categoryNameTv;
    private RecyclerView transactionsRecyclerView;
    private CategoryTransactionAdapter adapter;

    private TextView tvBuyingPriceVal;
    private TextView tvProfitVal;
    private TextView tvProfitLabel;

    public LinearLayout profitLayout;

    public static final String TRANS_TYPE_EXTRA= "trans_type";
    public static final String CASH_CATEGORY_ID= "cash_category_id";
    public static final String BRICK_INSTITUTION_ID= "brick_institution_id";
    public static final String START_DATE= "START_DATE";
    public static final String END_DATE= "END_DATE";

    private boolean allTxnSelected = false;

    public String getCustomerId(){
        return this.categoryId;
    }

    public void onCreate(Bundle bundle) {

        super.onCreate(bundle);

        setContentView((int) R.layout.activity_category_transactions);

        this.categoryId = getIntent().getStringExtra(CASH_CATEGORY_ID);
        this.brickInstitutionId = getIntent().getStringExtra(BRICK_INSTITUTION_ID);
        this.startDate = getIntent().getStringExtra(START_DATE);
        this.endDate = getIntent().getStringExtra(END_DATE);
        if (!Utility.isBlank(brickInstitutionId)) {
            this.categoryEntity = TransactionRepository.getInstance(getApplicationContext()).getCashCategoryByIdForBrick(this.brickInstitutionId,this.categoryId,startDate,endDate);
        } else {
            this.categoryEntity = CashRepository.getInstance(getApplicationContext()).getCashCategoryById(this.categoryId);
        }
        this.toolbar = findViewById(R.id.toolbar);
        this.categoryNameTv = toolbar.findViewById(R.id.name);
        this.tvRowStatus = findViewById(R.id.tvRowStatus);
        this.tvColMoneyIn = findViewById(R.id.tvColMoneyIn);
        this.tvBuyingPriceVal = findViewById(R.id.buying_amount_value);
        this.tvProfitVal = findViewById(R.id.profit_val);
        this.tvProfitLabel = findViewById(R.id.profit_label);
        this.profitLayout = findViewById(R.id.profit_layout);

        toolbar.findViewById(R.id.back_btn).setOnClickListener(this);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(false);
        refreshToolbar(this.categoryEntity);

        this.effectiveBalanceTv = findViewById(R.id.categoryBalance);
        this.balanceTypeStr = findViewById(R.id.balanceStatus);

        initTransactionListView();

        this.btnGiveMoney = findViewById(R.id.debitBtn);
        this.btnReceivePayment = findViewById(R.id.creditBtn);

        btnGiveMoney.setOnClickListener(new AddTransactionBtnClickListener(this, DEBIT));
        btnReceivePayment.setOnClickListener(new AddTransactionBtnClickListener(this, CREDIT));

        if(Utility.isBlank(brickInstitutionId)){
            ComponentUtil.setVisible(btnGiveMoney,categoryEntity.type<0);
            ComponentUtil.setVisible(btnReceivePayment,categoryEntity.type>=0);
        }

        setCustomerDetailClickHandler();

        setDeleteTransactionClickHandler();
        setChangeTransactionCategoryClickHandler();

    }

    private void setChangeTransactionCategoryClickHandler() {

        findViewById(R.id.changeCategory).setOnClickListener(v -> {


            if(checkIfAutoRecordTransactionsSelected()){
                Toast.makeText(this,getString(R.string.autorecord_category_change_error_message),Toast.LENGTH_SHORT).show();
                return;
            }
            List<String> selectedTransactionIds = getSelectedTransactionIds();
            if(selectedTransactionIds.size()>0){
                String creditCategories = RemoteConfigUtils.SelectCategory.INSTANCE.getCreditCategoriesNew();

                String debitCategories = RemoteConfigUtils.SelectCategory.INSTANCE.getDebitCategoriesNew();

                Gson gson = new GsonBuilder().create();
                Type jsonType = new TypeToken<ArrayList<Category>>(){}.getType();
                ArrayList<Category> allCategory = new ArrayList<>();

                if(categoryEntity.type == 1){
                    ArrayList<Category> creditCategoriesToDisplay = gson.fromJson(creditCategories, jsonType);
                    allCategory.addAll(creditCategoriesToDisplay.stream().map(cat -> new Category(cat.getCategoryImage(),cat.getCategoryName(),1,0,cat.getCategoryId())).collect(Collectors.toList()));
                }
                else if(categoryEntity.type == -1)
                {
                    ArrayList<Category> debitCategoriesToDisplay = gson.fromJson(debitCategories, jsonType);
                    allCategory.addAll(debitCategoriesToDisplay.stream().map(cat -> new Category(cat.getCategoryImage(),cat.getCategoryName(),-1,0,cat.getCategoryId())).collect(Collectors.toList()));
                }

                allCategory = (ArrayList<Category>) allCategory.stream().filter(it -> !it.getCategoryName().equals(categoryEntity.name)).collect(Collectors.toList());

                ChangeTransactionCategoryBottomSheet.Companion.newInstance(allCategory,selectedTransactionIds,categoryEntity.name,allTxnSelected).show(getSupportFragmentManager(), "ChangeTransactionCategoryBottomsheet");
            }
            else
            {
                //TODO add toast to select a transaction
            }

        });
    }

    private void setDeleteTransactionClickHandler() {
        findViewById(R.id.deleteTransaction).setOnClickListener(v -> {


            List<String> selectedTransactionIds = getSelectedTransactionIds();
            if(selectedTransactionIds.size()>0){


                Dialog dialog = new Dialog(this);
                dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
                dialog.setContentView(R.layout.dialog_delete_change_transaction);

                dialog.setCancelable(true);
                MaterialButton yesBtn = dialog.findViewById(R.id.yes_btn);
                MaterialButton noBtn = dialog.findViewById(R.id.no_btn);
                TextView title = dialog.findViewById(R.id.tv_title);
                TextView message = dialog.findViewById(R.id.tv_message);

                title.setText(getString(R.string.delete_transaction_alert_title,selectedTransactionIds.size()));
                message.setText(getString(R.string.delete_transaction_message));

                yesBtn.setText(getString(R.string.delete));
                yesBtn.setOnClickListener( yesBtnView -> {


                    AppAnalytics.PropBuilder builder = new AppAnalytics.PropBuilder();
                    builder.put(AnalyticsConst.SELECT_ALL_CHECKED, allTxnSelected ? AnalyticsConst.YES : AnalyticsConst.NO);
                    builder.put(AnalyticsConst.NO_TRANSACTION, selectedTransactionIds.size());
                    builder.put(ENTRY_POINT, BD);
                    builder.put(TYPE, TRANSAKSI);
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_PER_CATEGORY_DELETE_CONFIRMED, builder);

                    deleteSelectedCashTransactions(selectedTransactionIds);
                    dialog.dismiss();

                });

                noBtn.setOnClickListener(noBtnView -> {


                    dialog.dismiss();
                });
                dialog.show();
            }
            else{
                //TODO add toast to select a transaction
            }

        });
    }

    @NonNull
    private List<String> getSelectedTransactionIds() {
        List<String> selectedTransactionIds = new ArrayList<>();
        for (DataHolder holder : adapter.transactionDataHolderList) {
            if(holder.isChecked)
                selectedTransactionIds.add(((CategoryTransactionDataHolder)holder).getCashTransactionEntity().cashTransactionId);
        }
        return selectedTransactionIds;
    }

    @NonNull
    private boolean checkIfAutoRecordTransactionsSelected() {
        for (DataHolder holder : adapter.transactionDataHolderList) {
            if(((CategoryTransactionDataHolder)holder).getCashTransactionEntity().transactionType == TransactionEntityType.BRICK_TRANSACTION){
                return true;
            }
        }
        return false;
    }


    private void deleteSelectedCashTransactions(List<String> transactionIds) {
        Thread t = new Thread(new DeleteOrUpdateSelectedTransactionRunnable(this, transactionIds,categoryEntity.name,true,0, categoryId));
        t.start();
        try {
            t.join();
        } catch (java.lang.Exception e) {
            e.printStackTrace();
        }
    }

    private void setCustomerDetailClickHandler() {
        findViewById(R.id.btn_edit_category).setOnClickListener(new View.OnClickListener() {
            public void onClick (View v){
                Intent intent = new Intent(getBaseContext(), CashDetailActivity.class);
                intent.putExtra("categoryId", categoryId);
                startActivity(intent);
            }
        });

        ((CheckBox)findViewById(R.id.selectorAll)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(((CheckBox)v).isChecked()){
                    allTxnSelected = true;
                    adapter.bulkSelectUnselectItems(true);
                }else{
                    allTxnSelected = false;
                    adapter.bulkSelectUnselectItems(false);
                }
            }
        });

    }

    public void resetBulkSelector(){
        ((CheckBox)findViewById(R.id.selectorAll)).setChecked(false);
    }

    private void initTransactionListView() {

        this.transactionsRecyclerView = findViewById(R.id.transactionRV);
        if (!Utility.isBlank(brickInstitutionId)) {
            this.transactionListViewModel = new ViewModelProvider(this, new TransactionViewModelFactory(new Application(), this.categoryId, this.brickInstitutionId, true, startDate, endDate)).get(TransactionListViewModel.class);
        } else {
            this.transactionListViewModel = new ViewModelProvider(this, new TransactionViewModelFactory(new Application(), this.categoryId, this.brickInstitutionId, false, startDate, endDate)).get(TransactionListViewModel.class);
        }
        List arrayList = new ArrayList();
        this.adapter = new CategoryTransactionAdapter(arrayList, this.transactionsRecyclerView, this);
        this.transactionsRecyclerView.setAdapter(this.adapter);
        this.transactionsRecyclerView.setLayoutManager(new LinearLayoutManager(this));

        LifecycleOwner lifecycleOwner = this;
        try {
            if (Utility.isBlank(this.categoryId)) {
                FirebaseCrashlytics.getInstance().log("Empty category id: " + User.getUserId());
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        this.transactionListViewModel.getDataHolderList().observe(lifecycleOwner, new Observer<List<? extends DataHolder>>() {
            @Override
            public void onChanged(List<? extends DataHolder> list) {
                adapter.setDataHolderList(list);
            }
        });
        this.transactionListViewModel.getCategoryLiveData().observe(lifecycleOwner, new CategoryLiveDataObserver(this));

    }

    public void refreshToolbar(CashCategoryEntity categoryEntity) {
        if(categoryEntity!=null && categoryNameTv!=null) {
            this.categoryNameTv.setText(categoryEntity.name);
        }
    }

    public void displayIncomeSummary(CategorySummaryModel categorySummaryModel){
        this.tvBuyingPriceVal.setText(getFormattedBalance(categorySummaryModel.buyingPrice));
        double profit = categorySummaryModel.sellingPrice - categorySummaryModel.buyingPrice;
        if(profit<0) {
            this.tvProfitLabel.setText(this.getString(R.string.cash_in_loss_text));
            this.tvProfitVal.setText(getFormattedBalance(profit));
        }else{
            this.tvProfitLabel.setText(this.getString(R.string.cash_in_profit_text));
            this.tvProfitVal.setText(getFormattedBalance(profit));
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.back_btn:
                onBackPressed();
                break;
        }
    }

    private String getFormattedBalance(Double amount){
        StringBuilder sb = new StringBuilder();
        sb.append(Utility.getCurrency());
        sb.append(" ");
        sb.append(Utility.formatCurrency(Double.valueOf(Math.abs(amount))));
        return sb.toString();
    }
}
