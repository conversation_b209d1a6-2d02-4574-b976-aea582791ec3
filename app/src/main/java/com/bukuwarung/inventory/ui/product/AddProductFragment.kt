package com.bukuwarung.inventory.ui.product

import android.app.Activity.RESULT_OK
import android.os.Bundle
import android.os.SystemClock
import android.text.InputFilter
import android.text.TextUtils
import android.text.method.DigitsKeyListener
import android.view.*
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.activities.expense.ProductListActivity
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.MeasurementEntity
import com.bukuwarung.databinding.FragmentAddProductToInventoryBinding
import com.bukuwarung.inventory.dialog.CustomMeasurementUnitDialog
import com.bukuwarung.inventory.ui.StockUnitViewModel
import com.bukuwarung.inventory.ui.measurement.StockUnitBottomSheet
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.bukuwarung.utils.TooltipBuilder.Companion.builder
import com.google.android.material.bottomsheet.BottomSheetBehavior
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip
import javax.inject.Inject
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddProductFragment : BaseFragment(), StockUnitBottomSheet.StockUnitSelection, CustomMeasurementUnitDialog.AddMeasurementInterface {

    private val viewModel: AddProductViewModel by viewModels()
    private val stockUnitViewModel: StockUnitViewModel by viewModels()
    private var units = mutableListOf<MeasurementEntity>()

    private lateinit var allStockEdit: EditText
    private lateinit var minimumStockEdit: EditText
    private lateinit var minimumStockLayout: View
    private lateinit var allStockLayout: View
    private lateinit var productPriceParent: View
    private lateinit var selectUnit: Button
    private lateinit var addProductButton: Button
    private var tooltip: SimpleTooltip? = null
    private var dialog: CustomMeasurementUnitDialog? = null
    private var lastButtonSaveClicked: Long = 0
    private lateinit var sellingPriceEdit: TextView
    private lateinit var binding: FragmentAddProductToInventoryBinding
    private var globalProductCount = 0

    private var from: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = FragmentAddProductToInventoryBinding.inflate(inflater, container, false)
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        initKeyboard()
        minimumStockLayout = binding.minimumStockParent
        allStockLayout = binding.allStockParent

        allStockEdit = binding.currentStockEditLayout.etNumber
        minimumStockEdit = binding.minimumStockEditLayout.etNumber
        productPriceParent = binding.productPriceParent
        selectUnit = binding.selectUnit
        addProductButton = binding.addProductBtn
        sellingPriceEdit = binding.sellingPriceEdit
        binding.minimumStockEditLayout.etNumber.isEnabled = true
        binding.currentStockEditLayout.etNumber.isEnabled = true

        binding.currentStockEditLayout.etNumber.filters = arrayOf<InputFilter>(
            DigitsInputFilter(
                5,
                3,
                ','
            )
        )

        binding.minimumTooltip.setOnClickListener {

            context?.also {
                val tooltipBuilder = builder(it)
                        .setAnchor(binding.minimumTooltip)
                        .setText("Kamu akan mendapatkan \n" +
                                "pengingat ketika stok menipis")
                        .setGravity(Gravity.LEFT)
                tooltip = tooltipBuilder.build()
                tooltip?.show()
            }
        }

        binding.ivCurrentTooltip.setOnClickListener {
            context?.also {
                val tooltipBuilder = builder(it)
                    .setAnchor(binding.ivCurrentTooltip)
                    .setText(it.getString(R.string.decimal_qty_tooltip))
                    .setGravity(Gravity.START)
                tooltip = tooltipBuilder.build()
                tooltip?.show()
            }
        }

        stockUnitViewModel.init(User.getBusinessId(), "")

        observe()

        binding.currentStockEditLayout.btnDecrease.setOnClickListener {
            viewModel.maxStockCountDecreaseClick()
        }
        binding.currentStockEditLayout.btnIncrease.setOnClickListener {
            viewModel.maxStockCountIncreaseClick()
        }
        binding.addProductText.requestFocus()

        binding.addProductText.setOnFocusChangeListener { v, hasFocus ->
            if(hasFocus) {
                closeCalculatorKeyboard()
            }
        }

        binding.minimumStockEditLayout.etNumber.keyListener = DigitsKeyListener.getInstance("0123456789")

        binding.minimumStockEditLayout.etNumber.setOnFocusChangeListener { v, hasFocus ->
           if(hasFocus) {
               closeCalculatorKeyboard()
           }
        }
        binding.currentStockEditLayout.etNumber.setOnFocusChangeListener { v, hasFocus ->
            if(hasFocus) {
                closeCalculatorKeyboard()
            }
        }
        binding.sellingPriceParent.setOnClickListener {
            /*sellingPriceEdit.requestFocus()
            viewModel.productPriceFocusChange(true)*/
            changeCalculatorFocusToTrx()
        }
        sellingPriceEdit.doOnTextChanged { text, start, count, after ->
            viewModel.productPriceChange(text.toString())
        }

        binding.addProductText.doOnTextChanged { text, start, count, after ->
            viewModel.productNameChange(text.toString())
        }
        binding.currentStockEditLayout.etNumber.doOnTextChanged { text, start, count, after ->
            viewModel.allStockCountChnaged(text.toString())

        }
        binding.minimumStockEditLayout.etNumber.doOnTextChanged { text, start, count, after ->
            viewModel.minimumStockCountChange(text.toString())

        }
        binding.minimumStockEditLayout.btnDecrease.setOnClickListener {
            viewModel.minimumStockCountDecreaseClick()
        }
        binding.minimumStockEditLayout.btnIncrease.setOnClickListener {
            viewModel.minimumStockCountIncreaseClick()
        }

        binding.selectUnit.setOnClickListener {

            viewModel.onMeasurementClicked()
        }
        addProductButton.setOnClickListener {
            onAddProductButtonClicked(binding)
        }

        binding.ivClose.setOnClickListener {
            InputUtils.hideKeyboard(activity)
            activity?.finish()
        }
        viewModel.init(arguments?.getString(BOOK_ID) ?: "", arguments?.getBoolean(TRANSACTION_FLOW)
                ?: false)
        arguments?.getString(SEARCHED_PRODUCT)?.also {
            binding.addProductText.setText(it)
            try {
                binding.addProductText.setSelection(it.length)
            } catch (e: IndexOutOfBoundsException) {
                e.recordException()
            }

        }
        InputUtils.showKeyboard(activity)
        initBottomSheetBehavior(binding)
        return binding.root
    }

    private fun onAddProductButtonClicked(binding: FragmentAddProductToInventoryBinding) {
        if (SystemClock.elapsedRealtime() - lastButtonSaveClicked < 600 || TextUtils.isEmpty(binding.addProductText.text))
            return
        lastButtonSaveClicked = SystemClock.elapsedRealtime()

        val source = if(arguments?.getBoolean(TRANSACTION_FLOW) == true){
            AnalyticsConst.TXN_PROD_DETAIL_SOURCE
        }else{
            AnalyticsConst.STOCK_MENU_SOURCE
        }
        viewModel.addProduct(source)
    }

    fun observe() {
        viewModel.observerProductEvents.observe(viewLifecycleOwner, Observer {
            when (it) {
                AddProductViewModel.AddProductEvents.ShowLoading -> {
                }
                AddProductViewModel.AddProductEvents.HideLoading -> {
                }
                is AddProductViewModel.AddProductEvents.FinishScreen -> {
                    InputUtils.hideKeyboard(activity)
                    activity?.setResult(RESULT_OK)
                    activity?.finish()
                }
                is AddProductViewModel.AddProductEvents.FinishCurrentScreen -> {
                    InputUtils.hideKeyboard(activity)
                    arguments?.let { it ->
                        if (it.containsKey("from")) {
                            from = it.getString("from")
                        }
                    }

                    activity?.apply {
                        if (this is ProductListActivity) {
                            this.onProductCreated(it.product, from)
                        }
                        supportFragmentManager.beginTransaction().remove(this@AddProductFragment).commit()
                    }
                }
                is AddProductViewModel.AddProductEvents.ShowError -> {
                }
                is AddProductViewModel.AddProductEvents.MinimumStock -> {
                    try {
                        minimumStockEdit.setText(Integer(it.count).toString())
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                is AddProductViewModel.AddProductEvents.AllStock -> {
                    try {
                        allStockEdit.setText(Utility.getRoundedOffPrice(it.count))
                        allStockEdit.setSelection(allStockEdit.text.length)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                is AddProductViewModel.AddProductEvents.ShowStockNumberView -> {
                    if (!it.hideMaxMinStockUI) {
                        minimumStockLayout.visibility = View.VISIBLE
                        allStockLayout.visibility = View.VISIBLE
                    } else {
                        minimumStockLayout.visibility = View.GONE
                        allStockLayout.visibility = View.GONE
                    }
                    productPriceParent.visibility = View.VISIBLE
//                    minimumStockLayout.etNumber.setText(Integer.valueOf(it.minimumStockDefault).toString())
//                    allStockLayout.etNumber.setText(Integer.valueOf(it.allStockDefault).toString())
                    addProductButton.isClickable = true
                    activity?.also {
                        addProductButton.setBackgroundColor(ContextCompat.getColor(it, R.color.new_yellow))
                        addProductButton.setTextColor(ContextCompat.getColor(it, R.color.black_80))
                    }
                }
                AddProductViewModel.AddProductEvents.HideStockNumberView -> {
                    minimumStockLayout.visibility = View.VISIBLE
                    allStockLayout.visibility = View.VISIBLE
                    productPriceParent.visibility = View.VISIBLE
                    addProductButton.isClickable = false
                    activity?.also {
                        addProductButton.setBackgroundColor(ContextCompat.getColor(it, R.color.black_20))
                        addProductButton.setTextColor(ContextCompat.getColor(it, R.color.black_5))
                    }

                }
                is AddProductViewModel.AddProductEvents.OpenMeasurementBottomSheet -> {
                    activity?.apply {
                        val propBuilder = AppAnalytics.PropBuilder()
                        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.ADD_PRODUCT_SCREEN)
                        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.START)
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
                        StockUnitBottomSheet.instance(it.bookId, it.currentMeasurementId)
                                .show(childFragmentManager, "")
                        InputUtils.hideKeyboard(activity)
                    }
                }
                is AddProductViewModel.AddProductEvents.UpdateCurrentMeasurement -> {
                    selectUnit.text = it.measurementName
                }
                AddProductViewModel.AddProductEvents.ShowAddNewMeasurementDialog -> {
                    activity?.also {
                        dialog = CustomMeasurementUnitDialog(it, this)
                        dialog?.show()
                    }
                }
                AddProductViewModel.AddProductEvents.DismissAddNewMeasurementDialog -> {
                    Toast.makeText(activity, context?.getString(R.string.unit_successfully_saved), Toast.LENGTH_SHORT).show()
                    dialog?.dismiss()
                }
                is AddProductViewModel.AddProductEvents.MinMAxStockVisibility -> {
                    if(it.hide) {
                        minimumStockLayout.visibility = View.GONE
                        allStockLayout.visibility = View.GONE
                        productPriceParent.visibility = View.VISIBLE
                    }
                }
                is AddProductViewModel.AddProductEvents.SetProductCount -> globalProductCount = it.count
                else -> {}
            }
        })

        stockUnitViewModel.observerUnits.observe(viewLifecycleOwner, Observer {
            when (it) {
                is StockUnitViewModel.StockUnitEvents.ShowUnit -> {
                    units = it.units as MutableList<MeasurementEntity>
                }
                else -> {}
            }
        })
    }

    override fun setupView(view: View) {
    }

    override fun subscribeState() {
    }

    companion object {
        private const val BOOK_ID = "book_id"
        private const val TRANSACTION_FLOW = "transaction_flow"
        private const val SEARCHED_PRODUCT = "searched_product"

        fun instance(bookId: String, searchedProduct: String? = null, isTransactionFlow: Boolean = false): AddProductFragment {
            val fragment = AddProductFragment()
            val bundle = Bundle()
            bundle.putString(BOOK_ID, bookId)
            bundle.putBoolean(TRANSACTION_FLOW, isTransactionFlow)
            bundle.putString(SEARCHED_PRODUCT, searchedProduct)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onMeasurementSelected(unitId: String, unitName: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.ADD_PRODUCT_SCREEN)
        propBuilder.put(AnalyticsConst.MEASUREMENT_UNIT, unitName)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
        viewModel.onMeasurementChanged(unitId, unitName)
    }

    override fun addNewMeasurement() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.ADD_PRODUCT_SCREEN)
        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CREATE_NEW)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
        viewModel.addNewMeasurementRequest()
    }

    override fun addMeasurement(measurement: String) {
        units.forEach {
            if (it.measurementName == measurement) {
                InputUtils.hideKeyboard(requireContext())
                Toast.makeText(
                    requireActivity(),
                    context?.getString(R.string.unit_already_exists_please_use_another_name),
                    Toast.LENGTH_SHORT).show()
                return
            }
        }
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.ADD_PRODUCT_SCREEN)
        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CREATE_NEW)
        propBuilder.put(AnalyticsConst.MEASUREMENT_UNIT, measurement)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
        viewModel.addNewMeasurement(measurement)
    }

    override fun dismiss() {

    }



    private fun closeCalculatorKeyboard() {
        if (requireActivity().isAnimEnabled()) binding.keyboardView.clearAnimation()
        binding.keyboardView.visibility = View.GONE
        binding.keyboardView.cursor = binding.cursor
        binding.keyboardView.hideCursor()
    }

    private fun changeCalculatorFocusToTrx() {
        try {
            InputUtils.hideKeyBoardWithCheck(requireActivity())
            clearFocus()
            val moveup = AnimationUtils.loadAnimation(requireContext(), R.anim.move_up)
            if (requireActivity().isAnimEnabled()) binding.keyboardView.startAnimation(moveup)
            binding.keyboardView.visibility = View.VISIBLE
            binding.keyboardView.showCursor()
            binding.currencySymbol.visibility = View.VISIBLE
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun clearFocus() {
        binding.addProductText.clearFocus()
        binding.minimumStockEditLayout.etNumber.clearFocus()
        binding.currentStockEditLayout.etNumber.clearFocus()
    }

    private fun initKeyboard() {
        binding.keyboardView.setResultTv(binding.sellingPriceEdit)
        binding.keyboardView.cursor = binding.cursor
        binding.keyboardView.setCurrency(binding.currencySymbol)
        binding.keyboardView.setExprTv(binding.textAmountCalc)
        binding.keyboardView.setResultLayout(binding.resultLayout)
        binding.keyboardView.hideCursor()

    }

    private fun initBottomSheetBehavior(binding: FragmentAddProductToInventoryBinding) {
        val bottomSheetBehavior = BottomSheetBehavior.from(binding.llHeader)
        // Expanded by default
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
        bottomSheetBehavior.skipCollapsed = true
        bottomSheetBehavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {
                if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                    InputUtils.hideKeyboard(activity)
                    activity?.finish()
                    //Cancels animation on finish()
                    activity?.overridePendingTransition(0, 0)
                }
            }
            override fun onSlide(bottomSheet: View, slideOffset: Float) {
            }
        })
    }
}
