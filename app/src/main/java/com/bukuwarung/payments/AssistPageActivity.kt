package com.bukuwarung.payments

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.HelpCenterActivity
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.databinding.ActivityAssistPageBinding

import com.bukuwarung.payments.adapters.AssistPageAdapter
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PLN_POSTPAID
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.payments.viewmodels.AssistPageViewModel
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.setSingleClickListener
import javax.inject.Inject
import androidx.activity.viewModels
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AssistPageActivity : BaseActivity(), PaymentDownBottomSheet.PaymentDownBsListener {

    private lateinit var binding: ActivityAssistPageBinding
    private val viewModel: AssistPageViewModel by viewModels()
    private var positionSelected: Int = -1
    private val BTN_PROCEED = 999
    private val BTN_FETCH_ORDER_DETAIL = 998
    private var order: FinproOrderResponse? = null
    private val paymentType by lazy { intent?.getStringExtra(PAYMENT_TYPE) }
    private val disbursableType by lazy { intent?.getStringExtra(DISBURSABLE_TYPE) }
    private val customerName by lazy { intent?.getStringExtra(CUSTOMER_NAME) }
    private val orderId by lazy { intent?.getStringExtra(ORDER_ID) }
    private val ledgerAccountId by lazy { intent?.getStringExtra(LEDGER_ACCOUNT_ID) }
    private val qrisOrderId by lazy { intent?.getStringExtra(QRIS_ORDER_ID) }
    private val transactionTitle by lazy { intent?.getStringExtra(TRANSACTION_TITLE) }
    private var userProfileEntity: UserProfileEntity? = null
    private var assistOrderId: String? = null

    companion object {
        private const val ORDER_RESPONSE = "order_response"
        private const val PAYMENT_TYPE = "payment_type"
        private const val ORDER_ID = "order_id"
        private const val LEDGER_ACCOUNT_ID = "ledger_account_id"
        private const val QRIS_ORDER_ID = "qris_order_id"
        private const val DISBURSABLE_TYPE = "disbursable_type"
        private const val CUSTOMER_NAME = "customer_name"
        private const val TRANSACTION_TITLE = "transaction_title"

        fun createIntent(
            context: Context,
            order: FinproOrderResponse?,
            paymentType: String?,
            disbursableType: String?,
            customerName: String?,
            transactionTitle: String? = null,
            ledgerAccountId: String? = null,
            qrisOrderId: String? = null
        ): Intent = Intent(context, AssistPageActivity::class.java).apply {
            putExtra(ORDER_RESPONSE, order)
            putExtra(PAYMENT_TYPE, paymentType)
            putExtra(DISBURSABLE_TYPE, disbursableType)
            putExtra(CUSTOMER_NAME, customerName)
            putExtra(TRANSACTION_TITLE, transactionTitle)
            putExtra(LEDGER_ACCOUNT_ID, ledgerAccountId)
            putExtra(QRIS_ORDER_ID, qrisOrderId)
        }

        fun createIntent(
            context: Context,
            orderId: String?,
            paymentType: String?,
            disbursableType: String?,
            customerName: String?,
            transactionTitle: String? = null
        ): Intent = Intent(context, AssistPageActivity::class.java).apply {
            putExtra(ORDER_ID, orderId)
            putExtra(PAYMENT_TYPE, paymentType)
            putExtra(DISBURSABLE_TYPE, disbursableType)
            putExtra(CUSTOMER_NAME, customerName)
            putExtra(TRANSACTION_TITLE, transactionTitle)
        }

    }

    override fun setViewBinding() {
        binding = ActivityAssistPageBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        setToolBarView()

        viewModel.init(paymentType, disbursableType)
        userProfileEntity = viewModel.getUserProfile(
            SessionManager.getInstance().userId
        )
        order = intent.getParcelableExtra(ORDER_RESPONSE)
        if (order == null) {
            registerObserver()
            fetchOrderDetail()
        } else {
            setTopUI()
            setDetails()
            assistOrderId = if (viewModel.isQrisPaymentIn()) {
                qrisOrderId
            } else {
                order?.orderId
            }
        }
        binding.includeLayoutAssist.rvProblems.layoutManager = LinearLayoutManager(this)
        binding.includeLayoutAssist.rvProblems.adapter =
            AssistPageAdapter(resources.getStringArray(R.array.assist_problems).toList(), ::onClick)
    }

    private fun fetchOrderDetail() {
        if (Utility.hasInternet()) {
            orderId?.let { viewModel.getOrderDetail(it, ledgerAccountId) }
        } else {
            positionSelected = BTN_FETCH_ORDER_DETAIL
            showNoInternetBottomSheet(isServiceDown = false)
        }
    }

    private fun registerObserver() {
        viewModel.observeDetail.observe(this) {
            if (it is AssistPageViewModel.DetailEvent.ShowProductData)
                order = it.detail
            setTopUI()
            setDetails()
        }

        viewModel.viewState.observe(this) {
            if (it.internetError || it.serverError) {
                showNoInternetBottomSheet(isServiceDown = it.serverError)
            }
        }
    }

    private fun doProceed() {
        if (!Utility.hasInternet()) {
            positionSelected = BTN_PROCEED
            showNoInternetBottomSheet(false)
        } else {
            trackEvent(
                "general",
                paymentType.orEmpty(),
                order?.items?.getOrNull(0)?.name.orEmpty(),
                order?.payments?.firstOrNull()?.paymentMethod?.name ?: "-"
            )
            val item = order?.items?.firstOrNull()
            val category =
                if (viewModel.isPaymentPpob()) {
                    if (item?.beneficiary?.category == PpobConst.CATEGORY_LISTRIK && item.beneficiary.code == CATEGORY_PLN_POSTPAID) {
                        item.beneficiary.code
                    } else {
                        item?.beneficiary?.category
                    }
                } else {
                    null
                }
            val url =
                "${BuildConfig.ASSIST_TICKET_URL}?type=general&merchantId=${SessionManager.getInstance().userId}&transactionId=${order?.transactionId}&merchantName=${userProfileEntity?.userName}&paymentType=${paymentType}&orderId=${assistOrderId}&product=${item?.name}&category=${category}&uuid=${SessionManager.getInstance().uuid}&account_id=${SessionManager.getInstance().businessId}"
            val intent =
                WebviewActivity.createIntent(this, url, "")
            startActivity(intent)
        }
        binding.includeLayoutAssist.rvProblems.layoutManager = LinearLayoutManager(this)
        binding.includeLayoutAssist.rvProblems.adapter =
            AssistPageAdapter(
                resources.getStringArray(R.array.assist_problems).toList(),
                ::onClick
            )
    }

    private fun onClick(position: Int) {
        if (!Utility.hasInternet()) {
            this.positionSelected = position
            showNoInternetBottomSheet(false)
        } else {
            var url: String? = null
            var type: String? = null
            val item = order?.items?.firstOrNull()
            val category =
                if (viewModel.isPaymentPpob()) {
                    if (item?.beneficiary?.category == PpobConst.CATEGORY_LISTRIK && item.beneficiary.code == CATEGORY_PLN_POSTPAID) {
                        item.beneficiary.code
                    } else {
                        item?.beneficiary?.category
                    }
                } else {
                    null
                }
            when (position) {
                0 -> {
                    type = "refund"
                    url =
                        "${BuildConfig.ASSIST_TICKET_URL}?type=refund&merchantId=${SessionManager.getInstance().userId}&transactionId=${order?.transactionId}&merchantName=${userProfileEntity?.userName}&paymentType=${paymentType}&orderId=${assistOrderId}&product=${item?.name}&category=${category}&uuid=${SessionManager.getInstance().uuid}&account_id=${SessionManager.getInstance().businessId}"
                }
                1 -> {
                    type = "pending"
                    url =
                        "${BuildConfig.ASSIST_TICKET_URL}?type=pending&merchantId=${SessionManager.getInstance().userId}&transactionId=${order?.transactionId}&merchantName=${userProfileEntity?.userName}&paymentType=${paymentType}&orderId=${assistOrderId}&product=${item?.name}&category=${category}&uuid=${SessionManager.getInstance().uuid}&account_id=${SessionManager.getInstance().businessId}"
                }
                2 -> {
                    type = "double-deduction"
                    url =
                        "${BuildConfig.ASSIST_TICKET_URL}?type=double-deduction&merchantId=${SessionManager.getInstance().userId}&transactionId=${order?.transactionId}&merchantName=${userProfileEntity?.userName}&paymentType=${paymentType}&orderId=${assistOrderId}&product=${item?.name}&category=${category}&uuid=${SessionManager.getInstance().uuid}&account_id=${SessionManager.getInstance().businessId}"
                }
                else -> {
                    type = "refund"
                    url =
                        "${BuildConfig.ASSIST_TICKET_URL}?type=refund&merchantId=${SessionManager.getInstance().userId}&transactionId=${order?.transactionId}&merchantName=${userProfileEntity?.userName}&paymentType=${paymentType}&orderId=${assistOrderId}&product=${item?.name}&category=${category}&uuid=${SessionManager.getInstance().uuid}&account_id=${SessionManager.getInstance().businessId}"
                }
            }
            trackEvent(
                type,
                paymentType.orEmpty(),
                order?.items?.getOrNull(0)?.name.orEmpty(),
                order?.payments?.firstOrNull()?.paymentMethod?.name ?: "-"
            )
            val intent =
                WebviewActivity.createIntent(this, url, "")
            startActivity(intent)
        }
    }

    private fun showNoInternetBottomSheet(
        isServiceDown: Boolean,
        message: String? = null
    ) {
        val paymentDownBottomSheet =
            PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(supportFragmentManager, "PaymentDownBottomSheet")
    }

    override fun subscribeState() {
        // No implementation required
    }

    private fun setDetails() {
        val item = order?.items?.firstOrNull()
        binding.includeDetail.tvMerchantIdValue.text = SessionManager.getInstance().userId
        with(binding.includeDetail) {
            when {
                viewModel.isPaymentIn() -> {
                    grpPaymentIn.hideView()
                    grpPaymentOut.hideView()
                    tvPaymentMethod.text = getString(R.string.collect_money_from)
                    tvPaymentMethodValue.text = customerName
                    setIconAndName(PaymentHistory.TYPE_PAYMENT_IN)
                }
                viewModel.isQrisPaymentIn() -> {
                    grpPaymentIn.hideView()
                    grpPaymentOut.hideView()
                    tvPaymentMethod.text = getString(R.string.penerima)
                    tvPaymentMethodValue.text = customerName
                    setIconAndName(PaymentHistory.TYPE_PAYMENT_IN)
                }
                viewModel.isPaymentOut() -> {
                    grpPaymentOut.hideView()
                    tvPaymentMethodValue.text =
                        order?.payments?.firstOrNull()?.paymentMethod?.name ?: "-"
                    tvProduct.text = getString(R.string.pay_money_to)
                    tvProductValue.text = customerName
                    setIconAndName(PaymentHistory.TYPE_PAYMENT_OUT)
                }
                viewModel.isPaymentPpob() -> {
                    tvProductValue.text = item?.name
                    tvPaymentMethodValue.text =
                        order?.payments?.firstOrNull()?.paymentMethod?.name ?: "-"
                    tvNumberValue.text = item?.beneficiary?.phoneNumber
                    setIconAndName(item?.beneficiary?.category ?: "")
                    if (item?.beneficiary?.category == PpobConst.CATEGORY_LISTRIK && item.beneficiary.code == CATEGORY_PLN_POSTPAID) {
                        tvProduct.text = getString(R.string.customer_id_message)
                        tvProductValue.text = item.name
                        setIconAndName(CATEGORY_PLN_POSTPAID)
                    }
                }
                viewModel.isPaymentSaldoIn() -> {
                    grpPaymentIn.hideView()
                    grpPaymentOut.hideView()
                    tvPaymentMethodValue.text =
                        order?.payments?.firstOrNull()?.paymentMethod?.name ?: "-"
                    setIconAndName(PaymentHistory.TYPE_SALDO_IN)
                }
                viewModel.isSaldoRefund() -> {
                    grpPaymentIn.hideView()
                    grpPaymentOut.hideView()
                    tvPaymentMethodValue.text =
                        order?.payments?.firstOrNull()?.paymentMethod?.name ?: "-"
                    setIconAndName(PaymentHistory.TYPE_SALDO_REFUND)
                }
                viewModel.isPaymentSaldoOut() -> {
                    grpPaymentOut.hideView()
                    grpPaymentIn.hideView()
                    tvPaymentMethodValue.text =
                        order?.payments?.firstOrNull()?.paymentMethod?.name ?: "-"
                    setIconAndName(PaymentHistory.TYPE_SALDO_OUT)
                }
            }
        }
    }

    private fun setIconAndName(type: String) {
        val productNameAndIcon = PaymentUtils.getPaymentNameAndIcon(type)
        val title =
            transactionTitle
                ?: getString(productNameAndIcon?.get(PaymentUtils.TEXT_MESSAGE) as Int)
        binding.includeTop.tvProductName.text = title
        (productNameAndIcon?.get(PaymentUtils.ICON_RES) as Int?)?.let {
            binding.includeTop.ivIcon.setImageResource(it)
        }
    }

    private fun setTopUI() {
        with(binding.includeTop) {
            tvTransaction.text = order?.transactionId
            tvAmount.text = Utility.formatAmount(order?.amount)
            tvAppVersion.text = getString(R.string.app_version) + " " + BuildConfig.VERSION_NAME
            tvDate.text = DateTimeUtils.getFormattedLocalDateTime(
                order?.createdAt,
                DateTimeUtils.DD_MMM_YYYY_HH_MM
            )
            setStatus()
        }
    }

    private fun setStatus() {
        with(binding.includeTop) {
            when (order?.status) {
                PaymentHistory.STATUS_PENDING -> {
                    if (viewModel.isPaymentIn() || viewModel.isQrisPaymentIn()) {
                        tvStatus.text = getString(R.string.waiting_for_payment_in_status)
                    } else {
                        tvStatus.text = getString(R.string.waiting_for_payment_status)
                    }
                }
                PaymentHistory.STATUS_PAID -> {
                    if (viewModel.isQrisPaymentIn()) {
                        tvStatus.text = getString(R.string.waiting_for_payment_in_status)
                    } else {
                        tvStatus.text = getString(R.string.in_the_process_status)
                    }
                }

                PaymentHistory.STATUS_COMPLETED -> {
                    tvStatus.text = getString(R.string.success_label)
                }

                PaymentHistory.STATUS_EXPIRED -> {
                    tvStatus.text = getString(R.string.expired_status)
                }

                PaymentHistory.STATUS_FAILED -> {
                    when (order?.progress?.lastOrNull()?.state) {
                        PaymentHistory.STATUS_REFUNDING -> {
                            tvStatus.text = getString(R.string.automatic_refund_status)
                        }
                        PaymentHistory.STATUS_REFUNDED -> {
                            tvStatus.text = getString(R.string.automatic_refund_sucess_status)
                        }
                        PaymentHistory.STATUS_REFUNDING_FAILED -> {
                            tvStatus.text = getString(R.string.automatic_refund_failed_status)
                        }
                        else -> {
                            tvStatus.text = getString(R.string.failed_status)
                        }
                    }

                }
                else -> {

                }
            }
        }
    }

    private fun setToolBarView() {
        binding.includeToolBar.toolBarLabel.text = getString(R.string.assist_title)
        binding.includeToolBar.tbPpob.navigationIcon =
            ContextCompat.getDrawable(this, R.drawable.ic_arrow_back)
        binding.includeToolBar.tbPpob.setNavigationOnClickListener {
            onBackPressed()
        }
        binding.includeToolBar.ivHelp.setSingleClickListener {
            if (AppConfigManager.getInstance().useWebView()) {
                val intent = Intent(this, HelpCenterActivity::class.java)
                intent.putExtra(AppConst.URL, AppConst.BANTUAN)
                intent.putExtra(AppConst.TITLE, getString(R.string.help))
                startActivity(intent)
            } else {
                startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(AppConst.BANTUAN)))
            }
        }
    }

    private fun trackEvent(
        type: String,
        category: String,
        product: String,
        paymentMethod: String
    ) {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_INITIATE_IN_APP_TICKET,
            AppAnalytics.PropBuilder().put(
                AnalyticsConst.TYPE,
                type
            ).put(
                AnalyticsConst.CATEGORY,
                category
            ).put(
                AnalyticsConst.PRODUCT,
                product
            ).put(AnalyticsConst.METHOD_OF_PAYMENT, paymentMethod)
                .put(AnalyticsConst.MERCHANT_ID, SessionManager.getInstance().userId),
            false,
            false,
            false
        )
    }

    override fun onButtonClicked() {
        if (positionSelected == BTN_PROCEED) {
            doProceed()
        } else if (positionSelected == BTN_FETCH_ORDER_DETAIL) {
            fetchOrderDetail()
        } else if (positionSelected != -1)
            onClick(positionSelected)

    }
}